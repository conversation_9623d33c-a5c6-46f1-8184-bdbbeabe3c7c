package com.rutong.medical.admin.service.alarm.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.medical.admin.dto.alarm.SoundLightAlarmDTO;
import com.rutong.medical.admin.entity.alarm.SmAlarmConfig;
import com.rutong.medical.admin.mapper.alarm.SoundLightAlarmMapper;
import com.rutong.medical.admin.service.alarm.SoundLightAlarmService;
import com.rutong.medical.admin.vo.alarm.SoundLightAlarmVO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025-07-15
 * 声光报警设置服务实现类
 */
@Slf4j
@Service
public class SoundLightAlarmServiceImpl extends ServiceImpl<SoundLightAlarmMapper, SmAlarmConfig> implements SoundLightAlarmService {

    /**
     * 报警类型映射表
     */
    private static final Map<Integer, String> CONFIG_TYPE_MAP = new HashMap<>();
    
    /**
     * 缓存相关常量
     */
    private static final String ALARM_CONFIG_CACHE_KEY = "alarm:config:all";
    private static final String ALARM_CONFIG_BY_TYPE_CACHE_KEY = "alarm:config:type:";
    private static final int REDIS_CACHE_EXPIRE_SECONDS = 1800; // Redis缓存30分钟
    
    static {
        CONFIG_TYPE_MAP.put(9, "医护工卡");
        CONFIG_TYPE_MAP.put(7, "报警按钮");
        CONFIG_TYPE_MAP.put(8, "红外探测器");
        CONFIG_TYPE_MAP.put(5, "资产定位标签");
    }

    @Autowired
    private RedissonClient redissonClient;

    @Resource(name = "caffeineCacheManager")
    private CacheManager cacheManager;

    @Autowired
    private SoundLightAlarmMapper soundLightAlarmMapper;

    /**
     * 更新或查询报警配置（使用三级缓存）
     * 如果DTO为空，则返回所有报警配置
     * 如果DTO不为空，则根据configCodes批量更新对应的配置，并返回所有报警配置
     *
     * @param soundLightAlarmDTO 报警配置DTO
     * @return 所有报警配置列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SoundLightAlarmVO> updateAlarmConfig(SoundLightAlarmDTO soundLightAlarmDTO) {
        if (soundLightAlarmDTO != null && !CollectionUtils.isEmpty(soundLightAlarmDTO.getConfigCodes())) {
            // 批量更新配置
            LambdaQueryWrapper<SmAlarmConfig> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SmAlarmConfig::getConfigCode, soundLightAlarmDTO.getConfigCodes());
            
            SmAlarmConfig updateConfig = new SmAlarmConfig();
            updateConfig.setConfigValue(soundLightAlarmDTO.getConfigValue());
            updateConfig.setUpdateUserId(123456L); // TODO: 替换为实际用户ID
            updateConfig.setUpdateTime(new Date());
            this.update(updateConfig, queryWrapper);
            
            // 更新后清除所有相关缓存
            clearAllAlarmConfigCache();
        }

        // 使用三级缓存获取所有配置
        return getAllAlarmConfigWithCache();
    }




    @Override
    public List<SmAlarmConfig> getAll() {
        List<SmAlarmConfig> smAlarmConfigs = soundLightAlarmMapper.selectList(null);

        return smAlarmConfigs;
    }





    /**
     * 使用三级缓存获取所有报警配置
     * 缓存策略：Caffeine(本地) -> Redis(分布式) -> MySQL(数据库)
     *
     * @return 所有报警配置列表
     */
//    @Override
    public List<SoundLightAlarmVO> getAllAlarmConfigWithCache() {
        // 第一级：检查Caffeine本地缓存
        Cache caffeineCache = cacheManager.getCache("GLOBAL_CACHE");
        if (caffeineCache != null) {
            Cache.ValueWrapper wrapper = caffeineCache.get(ALARM_CONFIG_CACHE_KEY);
            if (wrapper != null && wrapper.get() != null) {
                log.debug("从Caffeine本地缓存获取报警配置数据");
                return (List<SoundLightAlarmVO>) wrapper.get();
            }
        }

        // 第二级：检查Redis分布式缓存
        RBucket<List<SoundLightAlarmVO>> redisBucket = redissonClient.getBucket(ALARM_CONFIG_CACHE_KEY);
        if (redisBucket.isExists()) {
            List<SoundLightAlarmVO> configList = redisBucket.get();
            if (configList != null && !configList.isEmpty()) {
                log.debug("从Redis分布式缓存获取报警配置数据");
                // 将数据存入Caffeine本地缓存
                if (caffeineCache != null) {
                    caffeineCache.put(ALARM_CONFIG_CACHE_KEY, configList);
                }
                return configList;
            }
        }

        // 第三级：从MySQL数据库查询
        log.debug("从MySQL数据库查询报警配置数据");
        List<SoundLightAlarmVO> configList = this.list().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 将数据存入Redis缓存
        if (!configList.isEmpty()) {
            redisBucket.set(configList, REDIS_CACHE_EXPIRE_SECONDS, TimeUnit.SECONDS);
            log.debug("报警配置数据已存入Redis缓存，过期时间：{}秒", REDIS_CACHE_EXPIRE_SECONDS);
        }

        // 将数据存入Caffeine本地缓存
        if (caffeineCache != null && !configList.isEmpty()) {
            caffeineCache.put(ALARM_CONFIG_CACHE_KEY, configList);
            log.debug("报警配置数据已存入Caffeine本地缓存");
        }

        return configList;
    }


    /**
     * 清除所有报警配置相关的缓存
     */
    private void clearAllAlarmConfigCache() {
        try {
            // 清除Caffeine本地缓存
            Cache caffeineCache = cacheManager.getCache("GLOBAL_CACHE");
            if (caffeineCache != null) {
                caffeineCache.evict(ALARM_CONFIG_CACHE_KEY);
                // 清除按类型缓存的数据
                for (Integer configType : CONFIG_TYPE_MAP.keySet()) {
                    caffeineCache.evict(ALARM_CONFIG_BY_TYPE_CACHE_KEY + configType);
                }
                log.debug("已清除Caffeine本地缓存中的报警配置数据");
            }

            // 清除Redis分布式缓存
            redissonClient.getBucket(ALARM_CONFIG_CACHE_KEY).deleteAsync();
            for (Integer configType : CONFIG_TYPE_MAP.keySet()) {
                redissonClient.getBucket(ALARM_CONFIG_BY_TYPE_CACHE_KEY + configType).deleteAsync();
            }
            log.debug("已清除Redis分布式缓存中的报警配置数据");

        } catch (Exception e) {
            log.error("清除报警配置缓存时发生异常", e);
        }
    }



    /**
     * 将实体转换为VO
     *
     * @param config 实体
     * @return VO
     */
    private SoundLightAlarmVO convertToVO(SmAlarmConfig config) {
        SoundLightAlarmVO vo = new SoundLightAlarmVO();
        BeanUtils.copyProperties(config, vo);

        // 设置报警类型名称
        vo.setConfigTypeName(getConfigTypeName(config.getConfigType()));

        return vo;
    }

    /**
     * 获取报警类型名称
     *
     * @param configType 报警类型代码
     * @return 报警类型名称
     */
    private String getConfigTypeName(Integer configType) {
        return CONFIG_TYPE_MAP.getOrDefault(configType, "未知类型");
    }
} 